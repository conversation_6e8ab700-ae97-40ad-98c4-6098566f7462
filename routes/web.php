<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\BeritaController;
use App\Http\Controllers\PrestasiController;
use App\Http\Controllers\SettingController;

Route::get('/', function () {
    return Inertia::render('landingpage/landing');
})->name('home');

Route::get('/landing-page', [BeritaController::class, 'index']);

Route::get('/landingPage', [BeritaController::class, 'index'])->name('landing');

// API endpoint untuk polling berita
Route::get('/api/berita', [BeritaController::class, 'getBeritaApi']);

// API endpoint untuk prestasi landing page
Route::get('/api/prestasi', [PrestasiController::class, 'getPrestasiForLanding']);

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');
    Route::get('data-guru', function() {
        return Inertia::render('data-guru');
    })->name('data-guru');

    // Berita
    Route::get('/berita', [BeritaController::class, 'berita']);
    Route::post('/add/berita', [BeritaController::class, 'addBerita']);
    Route::delete('/delete/berita/{id}', [BeritaController::class, 'destroy']);

    // Prestasi
    Route::get('/prestasi', [PrestasiController::class, 'index']);
    Route::post('/prestasi', [PrestasiController::class, 'store']);
    Route::delete('/prestasi/{id}', [PrestasiController::class, 'destroy']);

    // Setting
    Route::get('/setting', [SettingController::class, 'index']);
    Route::post('/setting/header', [SettingController::class, 'updateHeader']);

});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
