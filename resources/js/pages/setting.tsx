import { useState, useEffect } from 'react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm, usePage } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ChevronDown, ChevronUp, Image, FileText, Save, Upload, X, CheckCircle, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Setting',
        href: '/setting',
    },
];

interface PageProps {
    headerSettings?: {
        header_image?: string | null;
        header_description?: string;
    };
    flash?: {
        success?: string;
        error?: string;
    };
    [key: string]: any;
}

export default function Setting() {
    const { props } = usePage<PageProps>();
    const [isHeaderOpen, setIsHeaderOpen] = useState(false);
    const [imagePreview, setImagePreview] = useState<string | null>(null);

    const { data, setData, post, processing, errors } = useForm({
        header_image: '',
        header_description: props.headerSettings?.header_description || '',
    });

    // Set initial image preview if exists
    useEffect(() => {
        if (props.headerSettings?.header_image) {
            setImagePreview(props.headerSettings.header_image);
        }
    }, [props.headerSettings]);

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setData('header_image', file as any);

            // Create preview
            const reader = new FileReader();
            reader.onload = (e) => {
                setImagePreview(e.target?.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    const removeImage = () => {
        setData('header_image', '');
        setImagePreview(null);
        // Reset file input
        const fileInput = document.getElementById('header_image') as HTMLInputElement;
        if (fileInput) {
            fileInput.value = '';
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/setting/header', {
            onSuccess: () => {
                // Handle success
                console.log('Header settings updated successfully');
            },
            onError: (errors) => {
                console.error('Error updating header settings:', errors);
            }
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Setting" />

            <div className="space-y-6">
                {/* Flash Messages */}
                {props.flash?.success && (
                    <Alert className="border-green-200 bg-green-50 text-green-800">
                        <CheckCircle className="h-4 w-4" />
                        <AlertDescription>{props.flash.success}</AlertDescription>
                    </Alert>
                )}

                {props.flash?.error && (
                    <Alert className="border-red-200 bg-red-50 text-red-800">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>{props.flash.error}</AlertDescription>
                    </Alert>
                )}

                {/* Header Settings Card */}
                <Card className="w-full">
                    <Collapsible open={isHeaderOpen} onOpenChange={setIsHeaderOpen}>
                        <CollapsibleTrigger asChild>
                            <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors rounded-t-xl">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-3">
                                        <div className="p-2 bg-primary/10 rounded-lg">
                                            <Image className="w-5 h-5 text-primary" />
                                        </div>
                                        <div>
                                            <CardTitle className="text-lg font-semibold">
                                                Pengaturan Header
                                            </CardTitle>
                                            <CardDescription>
                                                Kelola gambar dan deskripsi header website
                                            </CardDescription>
                                        </div>
                                    </div>
                                    <Button variant="ghost" size="sm" className="p-2">
                                        {isHeaderOpen ? (
                                            <ChevronUp className="w-4 h-4" />
                                        ) : (
                                            <ChevronDown className="w-4 h-4" />
                                        )}
                                    </Button>
                                </div>
                            </CardHeader>
                        </CollapsibleTrigger>

                        <CollapsibleContent>
                            <CardContent className="pt-0">
                                <form onSubmit={handleSubmit} className="space-y-6">
                                    {/* Image Upload Section */}
                                    <div className="space-y-4">
                                        <div className="flex items-center gap-2">
                                            <Image className="w-4 h-4 text-muted-foreground" />
                                            <label className="text-sm font-medium">
                                                Gambar Header
                                            </label>
                                        </div>

                                        <div className="space-y-4">
                                            {/* Image Preview */}
                                            {imagePreview && (
                                                <div className="relative">
                                                    <div className="relative overflow-hidden rounded-lg border-2 border-dashed border-muted-foreground/25">
                                                        <img
                                                            src={imagePreview}
                                                            alt="Header preview"
                                                            className="w-full h-48 object-cover"
                                                        />
                                                        <Button
                                                            type="button"
                                                            variant="destructive"
                                                            size="sm"
                                                            className="absolute top-2 right-2"
                                                            onClick={removeImage}
                                                        >
                                                            <X className="w-4 h-4" />
                                                        </Button>
                                                    </div>
                                                </div>
                                            )}

                                            {/* File Input */}
                                            <div className="relative">
                                                <Input
                                                    id="header_image"
                                                    type="file"
                                                    accept="image/*"
                                                    onChange={handleImageChange}
                                                    className="hidden"
                                                />
                                                <label
                                                    htmlFor="header_image"
                                                    className={cn(
                                                        "flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer transition-colors",
                                                        imagePreview
                                                            ? "border-muted-foreground/25 bg-muted/25 hover:bg-muted/40"
                                                            : "border-muted-foreground/50 bg-muted/10 hover:bg-muted/25"
                                                    )}
                                                >
                                                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                                        <Upload className="w-8 h-8 mb-3 text-muted-foreground" />
                                                        <p className="mb-2 text-sm text-muted-foreground">
                                                            <span className="font-semibold">Klik untuk upload</span> atau drag and drop
                                                        </p>
                                                        <p className="text-xs text-muted-foreground">
                                                            PNG, JPG, JPEG (MAX. 2MB)
                                                        </p>
                                                    </div>
                                                </label>
                                            </div>

                                            {errors.header_image && (
                                                <p className="text-sm text-destructive">{errors.header_image}</p>
                                            )}
                                        </div>
                                    </div>

                                    {/* Description Section */}
                                    <div className="space-y-4">
                                        <div className="flex items-center gap-2">
                                            <FileText className="w-4 h-4 text-muted-foreground" />
                                            <label htmlFor="header_description" className="text-sm font-medium">
                                                Deskripsi Header
                                            </label>
                                        </div>

                                        <div className="space-y-2">
                                            <textarea
                                                id="header_description"
                                                value={data.header_description}
                                                onChange={(e) => setData('header_description', e.target.value)}
                                                placeholder="Masukkan deskripsi header website..."
                                                className="flex min-h-[120px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 resize-none"
                                                rows={5}
                                            />
                                            <p className="text-xs text-muted-foreground">
                                                Deskripsi ini akan ditampilkan di bagian header website
                                            </p>

                                            {errors.header_description && (
                                                <p className="text-sm text-destructive">{errors.header_description}</p>
                                            )}
                                        </div>
                                    </div>

                                    {/* Submit Button */}
                                    <div className="flex justify-end pt-4 border-t">
                                        <Button
                                            type="submit"
                                            disabled={processing}
                                            className="flex items-center gap-2"
                                        >
                                            <Save className="w-4 h-4" />
                                            {processing ? 'Menyimpan...' : 'Simpan Pengaturan'}
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </CollapsibleContent>
                    </Collapsible>
                </Card>
            </div>
        </AppLayout>
    );
}
