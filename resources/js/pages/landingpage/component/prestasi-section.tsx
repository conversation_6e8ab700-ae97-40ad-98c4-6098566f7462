import { useState } from 'react';
import { motion } from 'framer-motion';
import { Trophy, Star } from 'lucide-react';

interface PrestasiItem {
    id: number;
    nama: string;
    jenis: string;
    tahun: string;
    tingkat: string;
    peringkat: string;
}

export default function PrestasiSection() {
    const [prestasi] = useState<PrestasiItem[]>([
        {
            id: 1,
            nama: '<PERSON><PERSON>',
            jenis: 'Olimpiade Matematika Nasional',
            tahun: '2025',
            tingkat: 'Nasional',
            peringkat: 'Juara 1'
        },
        {
            id: 2,
            nama: '<PERSON><PERSON> Hidayat',
            jenis: 'Lomba Robotik Tingkat Provinsi',
            tahun: '2025',
            tingkat: 'Provinsi',
            peringkat: 'Juara 2'
        },
        {
            id: 3,
            nama: '<PERSON>',
            jenis: 'Kompetisi Bahasa Inggris',
            tahun: '2024',
            tingkat: 'Regional',
            peringkat: 'Juara 1'
        }
    ]);

    return (
        <section id="prestasi" className="py-20 bg-gray-50 dark:bg-gray-800">
            <div className="mx-auto max-w-7xl px-4">
                <motion.div
                    className="text-center mb-16"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <h3 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent">
                        Prestasi Membanggakan
                    </h3>
                    <p className="text-lg text-gray-700 dark:text-gray-200 max-w-2xl mx-auto">
                        Kebanggaan kami atas pencapaian luar biasa siswa-siswi berprestasi
                    </p>
                </motion.div>

                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                    {prestasi.map((item, index) => (
                        <motion.div
                            key={item.id}
                            className="bg-white dark:bg-gray-900 rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 group border border-gray-100 dark:border-gray-800 hover:border-green-200 dark:hover:border-green-700"
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: index * 0.1 }}
                            viewport={{ once: true }}
                            whileHover={{ y: -5 }}
                        >
                            <div className="flex items-start gap-4">
                                <div className="bg-gradient-to-r from-yellow-400 to-orange-500 p-3 rounded-full flex-shrink-0">
                                    <Trophy className="w-6 h-6 text-white" />
                                </div>
                                <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-2">
                                        <span className="bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 px-2 py-1 rounded-full text-xs font-semibold transition-colors duration-200">
                                            {item.tingkat}
                                        </span>
                                        <span className="text-gray-600 dark:text-gray-300 text-sm">
                                            {item.tahun}
                                        </span>
                                    </div>
                                    <h4 className="font-bold text-lg mb-2 text-gray-800 dark:text-white">
                                        {item.nama}
                                    </h4>
                                    <p className="text-gray-700 dark:text-gray-200 text-sm mb-3 leading-relaxed">
                                        {item.jenis}
                                    </p>
                                    <div className="flex items-center gap-2">
                                        <Star className="w-4 h-4 text-yellow-500 fill-current" />
                                        <span className="font-semibold text-yellow-600 dark:text-yellow-400">
                                            {item.peringkat}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </motion.div>
                    ))}
                </div>

                <motion.div
                    className="text-center mt-12"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    viewport={{ once: true }}
                >
                    <button className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-8 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200">
                        Lihat Semua Prestasi
                    </button>
                </motion.div>
            </div>
        </section>
    );
}