<?php

namespace App\Http\Controllers;
use Inertia\Inertia;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class SettingController extends Controller
{
    public function index()
    {
        // Get current header settings (you might want to store these in database)
        $headerSettings = [
            'header_image' => Storage::exists('public/header/header-image.jpg') ? '/storage/header/header-image.jpg' : null,
            'header_description' => config('app.header_description', ''),
        ];

        return Inertia::render('setting', [
            'headerSettings' => $headerSettings
        ]);
    }

    public function updateHeader(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'header_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'header_description' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            // Handle image upload
            if ($request->hasFile('header_image')) {
                // Delete old image if exists
                if (Storage::exists('public/header/header-image.jpg')) {
                    Storage::delete('public/header/header-image.jpg');
                }

                // Store new image
                $file = $request->file('header_image');
                $filename = 'header-image.' . $file->getClientOriginalExtension();
                $file->storeAs('public/header', $filename);
            }

            // Handle description (you might want to store this in database or config)
            if ($request->has('header_description')) {
                // For now, we'll just return success
                // In a real application, you might want to store this in a settings table
            }

            return redirect()->back()->with('success', 'Pengaturan header berhasil diperbarui!');

        } catch (\Exception $e) {
            Log::error('Error updating header settings: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Terjadi kesalahan saat memperbarui pengaturan header.');
        }
    }
}
